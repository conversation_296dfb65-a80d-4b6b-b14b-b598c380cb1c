-- FIX REGION SETTING FOR TEMPFLY.IO
-- This script ensures all inboxes have proper region values
-- Run this on your US database after the code fix

BEGIN;

-- Step 1: Check current region distribution
SELECT 
    'CURRENT_REGIONS' as status,
    region,
    COUNT(*) as count
FROM inboxes 
GROUP BY region
ORDER BY count DESC;

-- Step 2: Update any NULL regions to 'unknown' (shouldn't be any after your cleanup)
UPDATE inboxes 
SET region = 'unknown' 
WHERE region IS NULL;

-- Step 3: Show the result
SELECT 
    'AFTER_FIX' as status,
    region,
    COUNT(*) as count
FROM inboxes 
GROUP BY region
ORDER BY count DESC;

COMMIT;

-- Verification: Check that no inboxes have NULL region
SELECT 
    'VERIFICATION' as check_type,
    COUNT(*) as null_region_count,
    'Should be 0' as expected
FROM inboxes 
WHERE region IS NULL;
