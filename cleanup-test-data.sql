-- CLEANUP TEST DATA FOR TEMPFLY.IO
-- This script removes all test/development data before production launch
-- Run this on your US database to clean up the 83 test inboxes

BEGIN;

-- Step 1: Show what we're about to delete (for confirmation)
SELECT 
    'BEFORE_CLEANUP' as phase,
    COUNT(*) as total_inboxes,
    COUNT(CASE WHEN region = 'india' THEN 1 END) as india_region_inboxes,
    COUNT(CASE WHEN region = 'us' THEN 1 END) as us_region_inboxes,
    COUNT(CASE WHEN region IS NULL THEN 1 END) as null_region_inboxes
FROM inboxes;

-- Step 2: Show email count that will be deleted
SELECT 
    'EMAILS_TO_DELETE' as phase,
    COUNT(*) as total_emails
FROM emails;

-- Step 3: Show API keys that will be deleted
SELECT 
    'API_KEYS_TO_DELETE' as phase,
    COUNT(*) as total_api_keys
FROM api_keys WHERE deleted_at IS NULL;

-- Step 4: Delete all emails first (due to foreign key constraints)
DELETE FROM emails;

-- Step 5: Delete all inboxes
DELETE FROM inboxes;

-- Step 6: Delete all API keys (if any test ones exist)
DELETE FROM api_keys;

-- Step 7: Clean up any request logs (if they exist)
DELETE FROM request_logs WHERE created_at IS NOT NULL;

-- Step 8: Clean up any security logs (if they exist)
DELETE FROM security_logs WHERE created_at IS NOT NULL;

-- Step 9: Clean up any maintenance logs (if they exist)
DELETE FROM maintenance_logs WHERE created_at IS NOT NULL;

-- Step 10: Reset sequences to start fresh
-- This ensures new inboxes start with clean IDs
SELECT setval(pg_get_serial_sequence('domains', 'id'), 1, false);

-- Step 11: Verify cleanup
SELECT 
    'AFTER_CLEANUP' as phase,
    (SELECT COUNT(*) FROM inboxes) as total_inboxes,
    (SELECT COUNT(*) FROM emails) as total_emails,
    (SELECT COUNT(*) FROM api_keys) as total_api_keys,
    (SELECT COUNT(*) FROM domains) as total_domains
;

-- Step 12: Show remaining domains (these should stay)
SELECT 
    'REMAINING_DOMAINS' as info,
    domain,
    is_active,
    created_at
FROM domains 
ORDER BY domain;

COMMIT;

-- Final verification - all should be 0 except domains
SELECT 
    'FINAL_VERIFICATION' as check_type,
    'inboxes' as table_name,
    COUNT(*) as count
FROM inboxes
UNION ALL
SELECT 
    'FINAL_VERIFICATION' as check_type,
    'emails' as table_name,
    COUNT(*) as count
FROM emails
UNION ALL
SELECT 
    'FINAL_VERIFICATION' as check_type,
    'api_keys' as table_name,
    COUNT(*) as count
FROM api_keys
UNION ALL
SELECT 
    'FINAL_VERIFICATION' as check_type,
    'domains' as table_name,
    COUNT(*) as count
FROM domains;
