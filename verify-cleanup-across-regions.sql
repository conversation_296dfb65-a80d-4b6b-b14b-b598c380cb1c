-- VERIFICATION SCRIPT FOR MULTI-REGION CLEANUP
-- This script helps verify that cleanup was successful across all regions
-- Run this AFTER the cleanup to confirm replication worked properly

-- ⚠️  IMPORTANT: This can be run on ANY region to verify cleanup
-- ⚠️  However, allow 30-60 seconds for replication to complete after cleanup

SELECT 
    'CLEANUP_VERIFICATION' as check_type,
    NOW() as check_time,
    'Verifying cleanup across all TempFly.io regions' as description;

-- Check 1: Verify all tables are empty (except domains)
SELECT 
    'TABLE_COUNTS' as check_type,
    'inboxes' as table_name,
    COUNT(*) as count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ CLEAN'
        ELSE '❌ STILL HAS DATA'
    END as status
FROM inboxes
UNION ALL
SELECT 
    'TABLE_COUNTS' as check_type,
    'emails' as table_name,
    COUNT(*) as count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ CLEAN'
        ELSE '❌ STILL HAS DATA'
    END as status
FROM emails
UNION ALL
SELECT 
    'TABLE_COUNTS' as check_type,
    'api_keys' as table_name,
    COUNT(*) as count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ CLEAN'
        ELSE '❌ STILL HAS DATA'
    END as status
FROM api_keys
UNION ALL
SELECT 
    'TABLE_COUNTS' as check_type,
    'domains' as table_name,
    COUNT(*) as count,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ PRESERVED'
        ELSE '⚠️  NO DOMAINS'
    END as status
FROM domains
ORDER BY table_name;

-- Check 2: Show remaining domains (should be preserved)
SELECT 
    'REMAINING_DOMAINS' as check_type,
    domain,
    is_active,
    created_at,
    '✅ PRESERVED' as status
FROM domains 
ORDER BY domain;

-- Check 3: Check for any recent maintenance logs
SELECT 
    'MAINTENANCE_LOGS' as check_type,
    operation,
    details,
    status,
    executed_at
FROM maintenance_logs 
WHERE operation LIKE '%CLEANUP%' 
   OR operation LIKE '%PRODUCTION%'
ORDER BY executed_at DESC 
LIMIT 5;

-- Check 4: Regional data distribution (should be empty)
SELECT 
    'REGIONAL_DISTRIBUTION' as check_type,
    COALESCE(region, 'NULL_REGION') as region,
    COUNT(*) as inbox_count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ CLEAN'
        ELSE '❌ STILL HAS DATA'
    END as status
FROM inboxes 
GROUP BY region
UNION ALL
SELECT 
    'REGIONAL_DISTRIBUTION' as check_type,
    'TOTAL' as region,
    COUNT(*) as inbox_count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ ALL REGIONS CLEAN'
        ELSE '❌ DATA STILL EXISTS'
    END as status
FROM inboxes;

-- Check 5: Database size information
SELECT 
    'DATABASE_SIZE' as check_type,
    pg_size_pretty(pg_database_size(current_database())) as database_size,
    'After cleanup' as note;

-- Final summary
SELECT 
    'FINAL_STATUS' as check_type,
    CASE 
        WHEN (SELECT COUNT(*) FROM inboxes) = 0 
         AND (SELECT COUNT(*) FROM emails) = 0 
         AND (SELECT COUNT(*) FROM api_keys) = 0 
         AND (SELECT COUNT(*) FROM domains) > 0 
        THEN '✅ CLEANUP SUCCESSFUL - READY FOR PRODUCTION'
        ELSE '❌ CLEANUP INCOMPLETE - REVIEW ABOVE RESULTS'
    END as status,
    (SELECT COUNT(*) FROM inboxes) as remaining_inboxes,
    (SELECT COUNT(*) FROM emails) as remaining_emails,
    (SELECT COUNT(*) FROM domains) as preserved_domains;
