-- CRITICAL DATABASE SCHEMA FIX FOR INDIA REGION
-- This script fixes the missing tables and columns causing dashboard API failures
-- Run this on the India PostgreSQL database to synchronize schema with other regions

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

BEGIN;

-- Step 1: Add missing columns to inboxes table
DO $$
BEGIN
    -- Add deleted_at column for soft deletes
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'inboxes' AND column_name = 'deleted_at') THEN
        ALTER TABLE inboxes ADD COLUMN deleted_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added deleted_at column to inboxes table';
    END IF;

    -- Add region column for regional statistics
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'inboxes' AND column_name = 'region') THEN
        ALTER TABLE inboxes ADD COLUMN region VARCHAR(50) DEFAULT 'india';
        RAISE NOTICE 'Added region column to inboxes table';
    END IF;

    -- Add last_accessed_at column for activity tracking
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'inboxes' AND column_name = 'last_accessed_at') THEN
        ALTER TABLE inboxes ADD COLUMN last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added last_accessed_at column to inboxes table';
    END IF;
END $$;

-- Step 2: Add missing columns to api_keys table
DO $$
BEGIN
    -- Add deleted_at column for soft deletes
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'api_keys' AND column_name = 'deleted_at') THEN
        ALTER TABLE api_keys ADD COLUMN deleted_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added deleted_at column to api_keys table';
    END IF;

    -- Add subscription_tier column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'api_keys' AND column_name = 'subscription_tier') THEN
        ALTER TABLE api_keys ADD COLUMN subscription_tier VARCHAR(50) DEFAULT 'free';
        RAISE NOTICE 'Added subscription_tier column to api_keys table';
    END IF;
END $$;

-- Step 3: Add missing columns to emails table
DO $$
BEGIN
    -- Add deleted_at column for soft deletes
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'emails' AND column_name = 'deleted_at') THEN
        ALTER TABLE emails ADD COLUMN deleted_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added deleted_at column to emails table';
    END IF;
END $$;

-- Step 4: Create request_logs table for dashboard request statistics
CREATE TABLE IF NOT EXISTS request_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    client_ip INET,
    method VARCHAR(10) NOT NULL,
    endpoint VARCHAR(500) NOT NULL,
    status_code INTEGER NOT NULL,
    response_time_ms INTEGER,
    region VARCHAR(50) DEFAULT 'india',
    user_agent TEXT,
    headers JSONB,
    request_size INTEGER,
    response_size INTEGER,
    api_key_id UUID,
    rapidapi_key VARCHAR(255),
    error_message TEXT
);

-- Step 5: Create security_logs table for security statistics
CREATE TABLE IF NOT EXISTS security_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    level VARCHAR(20) NOT NULL DEFAULT 'info',
    message TEXT NOT NULL,
    client_ip INET,
    user_agent TEXT,
    endpoint VARCHAR(500),
    method VARCHAR(10),
    headers JSONB,
    metadata JSONB,
    region VARCHAR(50) DEFAULT 'india'
);

-- Step 6: Create maintenance_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS maintenance_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    operation VARCHAR(255) NOT NULL,
    details TEXT,
    affected_rows INTEGER DEFAULT 0,
    execution_time INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'success',
    error_message TEXT,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Step 7: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_inboxes_deleted_at ON inboxes(deleted_at);
CREATE INDEX IF NOT EXISTS idx_inboxes_region ON inboxes(region);
CREATE INDEX IF NOT EXISTS idx_inboxes_last_accessed_at ON inboxes(last_accessed_at);
CREATE INDEX IF NOT EXISTS idx_api_keys_deleted_at ON api_keys(deleted_at);
CREATE INDEX IF NOT EXISTS idx_api_keys_subscription_tier ON api_keys(subscription_tier);
CREATE INDEX IF NOT EXISTS idx_emails_deleted_at ON emails(deleted_at);

-- Request logs indexes
CREATE INDEX IF NOT EXISTS idx_request_logs_created_at ON request_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_request_logs_client_ip ON request_logs(client_ip);
CREATE INDEX IF NOT EXISTS idx_request_logs_endpoint ON request_logs(endpoint);
CREATE INDEX IF NOT EXISTS idx_request_logs_status_code ON request_logs(status_code);
CREATE INDEX IF NOT EXISTS idx_request_logs_region ON request_logs(region);
CREATE INDEX IF NOT EXISTS idx_request_logs_api_key_id ON request_logs(api_key_id);

-- Security logs indexes
CREATE INDEX IF NOT EXISTS idx_security_logs_created_at ON security_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_security_logs_level ON security_logs(level);
CREATE INDEX IF NOT EXISTS idx_security_logs_client_ip ON security_logs(client_ip);
CREATE INDEX IF NOT EXISTS idx_security_logs_region ON security_logs(region);

-- Maintenance logs indexes
CREATE INDEX IF NOT EXISTS idx_maintenance_logs_operation ON maintenance_logs(operation);
CREATE INDEX IF NOT EXISTS idx_maintenance_logs_executed_at ON maintenance_logs(executed_at);

-- Step 8: Update existing data with default values
-- Set region for existing inboxes based on environment or default to current region
-- This should be customized per region deployment
UPDATE inboxes SET region = COALESCE(
    CASE
        WHEN current_setting('app.region', true) IS NOT NULL THEN current_setting('app.region', true)
        ELSE 'unknown'
    END
) WHERE region IS NULL;

-- Set last_accessed_at for existing inboxes based on created_at
UPDATE inboxes SET last_accessed_at = COALESCE(last_activity_at, created_at) WHERE last_accessed_at IS NULL;

-- Set subscription_tier for existing api_keys
UPDATE api_keys SET subscription_tier = 'free' WHERE subscription_tier IS NULL;

-- Step 9: Log the migration completion
INSERT INTO maintenance_logs (
    operation,
    details,
    status
) VALUES (
    'INDIA_SCHEMA_MIGRATION',
    'Fixed missing tables and columns for dashboard API compatibility',
    'success'
);

COMMIT;

-- Step 10: Verification queries
SELECT 'VERIFICATION: Tables created successfully' as status;

-- Verify inboxes table has required columns
SELECT 
    'inboxes_columns' as table_check,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'inboxes' 
AND column_name IN ('deleted_at', 'region', 'last_accessed_at')
ORDER BY column_name;

-- Verify api_keys table has required columns
SELECT 
    'api_keys_columns' as table_check,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'api_keys' 
AND column_name IN ('deleted_at', 'subscription_tier')
ORDER BY column_name;

-- Verify request_logs table exists and has required columns
SELECT 
    'request_logs_table' as table_check,
    COUNT(*) as column_count
FROM information_schema.columns 
WHERE table_name = 'request_logs';

-- Verify security_logs table exists
SELECT 
    'security_logs_table' as table_check,
    COUNT(*) as column_count
FROM information_schema.columns 
WHERE table_name = 'security_logs';

-- Show all tables in the database
SELECT 
    'all_tables' as check_type,
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;
